import { Wix_Madefor_Display, Wix_Madefor_Text } from "next/font/google";
import "./globals.css";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { ThemeProvider } from "@/components/theme-provider";
import { ThemeToggle } from "@/components/ui/theme-toggle";

const wixMadeforDisplay = Wix_Madefor_Display({
  variable: "--font-wix-display",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

const wixMadeforText = Wix_Madefor_Text({
  variable: "--font-wix-text",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800"],
});

export const metadata = {
  title: "Auris Compliance - Streamline Your Compliance Management",
  description: "Intelligent compliance management platform for modern businesses. Automate frameworks like ISO 27001, HIPAA, SOC 2, and more.",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${wixMadeforDisplay.variable} ${wixMadeforText.variable} antialiased font-sans`}
      >
        <ThemeProvider defaultTheme="dark" storageKey="auris-ui-theme">
          <div className="flex min-h-screen flex-col">
            <Header />
            <main className="flex-1 pt-24">{children}</main>
            <Footer />
            <ThemeToggle />
          </div>
        </ThemeProvider>
      </body>
    </html>
  );
}

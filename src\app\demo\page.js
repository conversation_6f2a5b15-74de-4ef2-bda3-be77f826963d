import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/Button";
import { Play, Monitor, CheckCircle } from "lucide-react";

export default function DemoPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted/50">
      <div className="container mx-auto px-4">
        {/* Demo Options */}
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2 max-w-4xl mx-auto">
          {/* Watch Demo */}
          <div className="rounded-lg border bg-background p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6 mx-auto">
              <Play className="h-8 w-8 text-primary-foreground" />
            </div>
            <h2 className="text-2xl font-bold mb-4 text-center">Watch Product Demo</h2>
            <p className="text-muted-foreground mb-6 text-center">
              Get an overview of the Auris GRCOS platform with our comprehensive product demonstration video.
              See key features, workflows, and capabilities in action.
            </p>
            <ul className="space-y-2 mb-8">
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Platform overview and architecture
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Key modules and integrations
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Compliance workflow examples
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Reporting and analytics features
              </li>
            </ul>
            <Button size="lg" className="w-full" asChild>
              <Link href="/demo/watch">
                <Play className="mr-2 h-4 w-4" />
                Watch Demo Video
              </Link>
            </Button>
          </div>

          {/* Interactive Demo */}
          <div className="rounded-lg border bg-background p-8 shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-primary mb-6 mx-auto">
              <Monitor className="h-8 w-8 text-primary-foreground" />
            </div>
            <h2 className="text-2xl font-bold mb-4 text-center">Try Interactive Demo</h2>
            <p className="text-muted-foreground mb-6 text-center">
              Experience Auris GRCOS firsthand with our interactive demo environment. 
              Click through real workflows and explore the platform at your own pace.
            </p>
            <ul className="space-y-2 mb-8">
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Full platform access with sample data
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Interactive guided tutorials
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                Explore all modules and features
              </li>
              <li className="flex items-center text-sm">
                <CheckCircle className="h-4 w-4 text-primary mr-2 flex-shrink-0" />
                No installation or setup required
              </li>
            </ul>
            <Button size="lg" className="w-full" variant="outline" asChild>
              <Link href="https://demo.auriscompliance.com" target="_blank" rel="noopener noreferrer">
                <Monitor className="mr-2 h-4 w-4" />
                Launch Interactive Demo
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/Button";
import { GlowingStarBackground } from "@/components/ui/dot-background";
import { ArrowRight, Mail, Users, Zap, CheckCircle, Star } from "lucide-react";

export default function WaitlistPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <GlowingStarBackground containerClassName="flex-1" stars={300} columns={30}>
        {/* Hero Section */}
        <section className="relative pt-32 pb-20 lg:pt-40 lg:pb-32">
          <div className="absolute inset-0 bg-white/10 dark:bg-black/10 backdrop-blur-md border-b border-white/20 dark:border-white/10"></div>
          <div className="container mx-auto px-4 relative z-10">
            <div className="mx-auto max-w-4xl text-center">
              <div className="inline-flex items-center rounded-full bg-primary/20 backdrop-blur-lg px-3 py-1 text-sm font-medium text-primary mb-6 border border-primary/30 shadow-lg">
                <Star className="h-4 w-4 mr-2" />
                Pre-Seed Startup • Early Access
              </div>

              {/* Benefits Pills */}
              <div className="flex flex-wrap justify-center gap-3 mb-8">
                <div className="inline-flex items-center rounded-full bg-blue-500/20 backdrop-blur-lg px-3 py-1 text-sm font-medium text-blue-600 dark:text-blue-400 border border-blue-500/30 shadow-lg">
                  <Zap className="h-4 w-4 mr-2" />
                  Early Access • Priority Onboarding
                </div>
                <div className="inline-flex items-center rounded-full bg-green-500/20 backdrop-blur-lg px-3 py-1 text-sm font-medium text-green-600 dark:text-green-400 border border-green-500/30 shadow-lg">
                  <Users className="h-4 w-4 mr-2" />
                  Founder Access • Direct Feedback
                </div>
                <div className="inline-flex items-center rounded-full bg-purple-500/20 backdrop-blur-lg px-3 py-1 text-sm font-medium text-purple-600 dark:text-purple-400 border border-purple-500/30 shadow-lg">
                  <Star className="h-4 w-4 mr-2" />
                  Special Pricing • Extended Trials
                </div>
              </div>

              <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">
                Join the <span className="text-primary">Waitlist</span>
              </h1>
              <p className="mt-6 text-lg leading-8 text-muted-foreground sm:text-xl">
                Be among the first to experience Auris GRCOS when we launch. Get early access to our
                revolutionary compliance management platform and help shape the future of regulatory automation.
              </p>
            </div>
          </div>
        </section>

        {/* Waitlist Form */}
        <section className="relative py-10 pb-40 lg:pb-48">
            <div className="container mx-auto px-4">
            <div className="mx-auto max-w-2xl">
              <div className="rounded-lg border bg-background/95 backdrop-blur-md p-8 shadow-lg border-white/10">
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold mb-2">Reserve Your Spot</h2>
                  <p className="text-muted-foreground">
                    Join over 500+ compliance professionals already on our waitlist
                  </p>
                </div>

                <form className="space-y-6">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium mb-2">
                        First Name *
                      </label>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        required
                        className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="John"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium mb-2">
                        Last Name *
                      </label>
                      <input
                        type="text"
                        id="lastName"
                        name="lastName"
                        required
                        className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                        placeholder="Doe"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-2">
                      Work Email *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label htmlFor="company" className="block text-sm font-medium mb-2">
                      Company *
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      required
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                      placeholder="Acme Corp"
                    />
                  </div>

                  <div>
                    <label htmlFor="role" className="block text-sm font-medium mb-2">
                      Role
                    </label>
                    <select
                      id="role"
                      name="role"
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="">Select your role</option>
                      <option value="compliance-officer">Compliance Officer</option>
                      <option value="risk-manager">Risk Manager</option>
                      <option value="ciso">CISO</option>
                      <option value="cto">CTO</option>
                      <option value="ceo">CEO</option>
                      <option value="legal">Legal Counsel</option>
                      <option value="auditor">Auditor</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="companySize" className="block text-sm font-medium mb-2">
                      Company Size
                    </label>
                    <select
                      id="companySize"
                      name="companySize"
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    >
                      <option value="">Select company size</option>
                      <option value="1-10">1-10 employees</option>
                      <option value="11-50">11-50 employees</option>
                      <option value="51-200">51-200 employees</option>
                      <option value="201-1000">201-1000 employees</option>
                      <option value="1000+">1000+ employees</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="frameworks" className="block text-sm font-medium mb-2">
                      Compliance Frameworks of Interest
                    </label>
                    <div className="grid grid-cols-2 gap-2 mt-2">
                      {["ISO 27001", "SOC 2", "HIPAA", "PCI DSS", "GDPR", "POPIA"].map((framework) => (
                        <label key={framework} className="flex items-center">
                          <input
                            type="checkbox"
                            name="frameworks"
                            value={framework}
                            className="mr-2 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                          />
                          <span className="text-sm">{framework}</span>
                        </label>
                      ))}
                    </div>
                  </div>

                  <Button type="submit" size="lg" className="w-full">
                    <Mail className="mr-2 h-4 w-4" />
                    Join Waitlist
                  </Button>

                  <p className="text-xs text-muted-foreground text-center">
                    By joining our waitlist, you agree to receive updates about Auris GRCOS.
                    We respect your privacy and won&apos;t spam you.
                  </p>
                </form>
              </div>
            </div>
          </div>
      </section>
      </GlowingStarBackground>
    </div>
  );
}
